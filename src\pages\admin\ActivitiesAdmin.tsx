import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  <PERSON>Off,
  <PERSON>,
  <PERSON>Off,
  Filter,
  Chevron<PERSON><PERSON><PERSON>,
  ChevronRight,
  Loader2,
  Calendar,
  Clock,
  DollarSign,
} from "lucide-react";
import { toast } from "sonner";

import {
  getActivities,
  deleteActivity,
  updateActivityStatus,
  toggleActivityFeatured,
} from "@/lib/activities";
import type {
  Activity,
  ActivityFilters,
  ActivitySort,
  ActivityStatus,
} from "@/types/activity";
import {
  ACTIVITY_CATEGORY_LABELS,
  ACTIVITY_STATUS_CONFIG,
  ACTIVITY_CATEGORY_COLORS,
  DEFAULT_ACTIVITY_FILTERS,
  DEFAULT_ACTIVITY_SORT,
} from "@/types/activity";

export default function ActivitiesAdmin() {
  // const navigate = useNavigate();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<ActivityFilters>(
    DEFAULT_ACTIVITY_FILTERS
  );
  const [sort, setSort] = useState<ActivitySort>(DEFAULT_ACTIVITY_SORT);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null);
  const [updatingFeatured, setUpdatingFeatured] = useState<string | null>(null);

  // Load activities
  const loadActivities = async () => {
    try {
      setLoading(true);
      const response = await getActivities(filters, sort, {
        page: pagination.page,
        limit: pagination.limit,
      });
      setActivities(response.data);
      setPagination(response.pagination);
    } catch (error) {
      console.error("Error loading activities:", error);
      toast.error("Failed to load activities");
    } finally {
      setLoading(false);
    }
  };

  // Load activities on component mount and when filters/sort/pagination change
  useEffect(() => {
    loadActivities();
  }, [filters, sort, pagination.page]);

  // Handle search
  const handleSearch = (value: string) => {
    setFilters({ ...filters, search: value });
    setPagination({ ...pagination, page: 1 });
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof ActivityFilters, value: any) => {
    setFilters({ ...filters, [key]: value });
    setPagination({ ...pagination, page: 1 });
  };

  // Handle sort changes
  const handleSort = (field: ActivitySort["field"]) => {
    const newDirection =
      sort.field === field && sort.direction === "asc" ? "desc" : "asc";
    setSort({ field, direction: newDirection });
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setPagination({ ...pagination, page: newPage });
  };

  // Handle delete activity
  const handleDelete = async (id: string) => {
    try {
      setDeletingId(id);
      const result = await deleteActivity(id);
      if (result.success) {
        toast.success("Activity deleted successfully");
        loadActivities();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error deleting activity:", error);
      toast.error("Failed to delete activity");
    } finally {
      setDeletingId(null);
    }
  };

  // Handle status toggle
  const handleStatusToggle = async (
    id: string,
    currentStatus: ActivityStatus
  ) => {
    try {
      setUpdatingStatus(id);
      const newStatus =
        currentStatus === "published" ? "unpublished" : "published";
      const result = await updateActivityStatus(id, newStatus);
      if (result.success) {
        toast.success(`Activity ${newStatus} successfully`);
        loadActivities();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error updating activity status:", error);
      toast.error("Failed to update activity status");
    } finally {
      setUpdatingStatus(null);
    }
  };

  // Handle featured toggle
  const handleFeaturedToggle = async (id: string, currentFeatured: boolean) => {
    try {
      setUpdatingFeatured(id);
      const result = await toggleActivityFeatured(id, !currentFeatured);
      if (result.success) {
        toast.success(
          `Activity ${
            !currentFeatured ? "featured" : "unfeatured"
          } successfully`
        );
        loadActivities();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error updating activity featured status:", error);
      toast.error("Failed to update activity featured status");
    } finally {
      setUpdatingFeatured(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Activities</h1>
          <p className="text-gray-600 mt-2">
            Manage safari experiences and tours
          </p>
        </div>
        <Link to="/admin/activities/new">
          <Button className="bg-amber-600 hover:bg-amber-700">
            <Plus className="w-4 h-4 mr-2" />
            Add Activity
          </Button>
        </Link>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="search"
                  placeholder="Search activities..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="space-y-2">
              <Label>Category</Label>
              <Select
                value={filters.category}
                onValueChange={(value) => handleFilterChange("category", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {Object.entries(ACTIVITY_CATEGORY_LABELS).map(
                    ([key, label]) => (
                      <SelectItem key={key} value={key}>
                        {label}
                      </SelectItem>
                    )
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <Label>Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => handleFilterChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="unpublished">Unpublished</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Featured Filter */}
            <div className="space-y-2">
              <Label>Featured</Label>
              <Select
                value={filters.featured.toString()}
                onValueChange={(value) =>
                  handleFilterChange(
                    "featured",
                    value === "all" ? "all" : value === "true"
                  )
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All activities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Activities</SelectItem>
                  <SelectItem value="true">Featured Only</SelectItem>
                  <SelectItem value="false">Not Featured</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activities Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Activities ({pagination.total})</CardTitle>
            <div className="text-sm text-gray-500">
              Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
              {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
              of {pagination.total} activities
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-8 h-8 animate-spin text-amber-600" />
              <span className="ml-2 text-gray-600">Loading activities...</span>
            </div>
          ) : activities.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No activities found
              </h3>
              <p className="text-gray-500 mb-4">
                {filters.search ||
                filters.category !== "all" ||
                filters.status !== "all"
                  ? "Try adjusting your filters to see more activities."
                  : "Get started by creating your first activity."}
              </p>
              <Link to="/admin/activities/new">
                <Button className="bg-amber-600 hover:bg-amber-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Activity
                </Button>
              </Link>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort("title")}
                      >
                        Title
                        {sort.field === "title" && (
                          <span className="ml-1">
                            {sort.direction === "asc" ? "↑" : "↓"}
                          </span>
                        )}
                      </TableHead>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort("category")}
                      >
                        Category
                        {sort.field === "category" && (
                          <span className="ml-1">
                            {sort.direction === "asc" ? "↑" : "↓"}
                          </span>
                        )}
                      </TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort("pricing")}
                      >
                        Price
                        {sort.field === "pricing" && (
                          <span className="ml-1">
                            {sort.direction === "asc" ? "↑" : "↓"}
                          </span>
                        )}
                      </TableHead>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort("status")}
                      >
                        Status
                        {sort.field === "status" && (
                          <span className="ml-1">
                            {sort.direction === "asc" ? "↑" : "↓"}
                          </span>
                        )}
                      </TableHead>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort("featured")}
                      >
                        Featured
                        {sort.field === "featured" && (
                          <span className="ml-1">
                            {sort.direction === "asc" ? "↑" : "↓"}
                          </span>
                        )}
                      </TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {activities.map((activity) => (
                      <TableRow key={activity.id} className="hover:bg-gray-50">
                        <TableCell>
                          <div className="font-medium">{activity.title}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {activity.description}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={`${
                              ACTIVITY_CATEGORY_COLORS[activity.category].bg
                            } ${
                              ACTIVITY_CATEGORY_COLORS[activity.category].text
                            } ${
                              ACTIVITY_CATEGORY_COLORS[activity.category].border
                            }`}
                          >
                            {ACTIVITY_CATEGORY_LABELS[activity.category]}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm">
                            <Clock className="w-4 h-4 mr-1 text-gray-400" />
                            {activity.duration}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm font-medium">
                            <DollarSign className="w-4 h-4 mr-1 text-gray-400" />
                            {activity.pricing}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={`${
                              ACTIVITY_STATUS_CONFIG[activity.status].bgColor
                            } ${ACTIVITY_STATUS_CONFIG[activity.status].color}`}
                          >
                            {ACTIVITY_STATUS_CONFIG[activity.status].label}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              handleFeaturedToggle(
                                activity.id,
                                activity.featured
                              )
                            }
                            disabled={updatingFeatured === activity.id}
                            className="p-1"
                          >
                            {updatingFeatured === activity.id ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : activity.featured ? (
                              <Star className="w-4 h-4 text-amber-500 fill-current" />
                            ) : (
                              <StarOff className="w-4 h-4 text-gray-400" />
                            )}
                          </Button>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end space-x-2">
                            {/* Status Toggle */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                handleStatusToggle(activity.id, activity.status)
                              }
                              disabled={updatingStatus === activity.id}
                              className="p-1"
                              title={
                                activity.status === "published"
                                  ? "Unpublish"
                                  : "Publish"
                              }
                            >
                              {updatingStatus === activity.id ? (
                                <Loader2 className="w-4 h-4 animate-spin" />
                              ) : activity.status === "published" ? (
                                <Eye className="w-4 h-4 text-green-600" />
                              ) : (
                                <EyeOff className="w-4 h-4 text-gray-400" />
                              )}
                            </Button>

                            {/* Edit */}
                            <Link to={`/admin/activities/edit/${activity.id}`}>
                              <Button variant="ghost" size="sm" className="p-1">
                                <Edit className="w-4 h-4 text-blue-600" />
                              </Button>
                            </Link>

                            {/* Delete */}
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="p-1 text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    Delete Activity
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete "
                                    {activity.title}"? This action cannot be
                                    undone and will permanently remove the
                                    activity and all associated images.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDelete(activity.id)}
                                    disabled={deletingId === activity.id}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    {deletingId === activity.id ? (
                                      <>
                                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                        Deleting...
                                      </>
                                    ) : (
                                      "Delete Activity"
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-gray-500">
                    Page {pagination.page} of {pagination.totalPages}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page === 1}
                    >
                      <ChevronLeft className="w-4 h-4 mr-1" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Next
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
