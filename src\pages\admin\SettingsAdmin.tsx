// =====================================================
// Settings Module - Configuration Management
// =====================================================
// Comprehensive settings management interface for lodge configuration

import { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Save,
  Phone,
  Mail,
  Clock,
  DollarSign,
  TestTube,
  Plus,
  Trash2,
} from "lucide-react";

// Import types and services
import type { SettingsFormState } from "@/types/settings";
import {
  getLodgeContacts,
  getCheckInPolicy,
  getChildPricingPolicy,
  getPricingDefaults,
  getEmailSettings,
  getEmailTemplates,
  getDisplaySettings,
  getBusinessInfo,
  updateLodgeContacts,
  updateCheckInPolicy,
  updateChildPricingPolicy,
  updatePricingDefaults,
  updateEmailSettings,
  updateEmailTemplates,
  updateDisplaySettings,
  updateBusinessInfo,
  bulkUpdateSettings,
} from "@/lib/settings";
import { testEmailConfiguration } from "@/lib/email";

export default function SettingsAdmin() {
  // State management
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("contacts");

  // Form state
  const [formData, setFormData] = useState<SettingsFormState>({
    contacts: {
      emails: {
        reservations: "",
        sales: "",
        info: "",
        manager: "",
      },
      phones: [],
      address: {
        street: "",
        city: "",
        country: "",
        postal_code: "",
      },
    },
    checkInPolicy: {
      check_in_time: "12:00",
      check_in_end: "18:00",
      check_out_time: "10:00",
      late_checkout_fee: 0,
      early_checkin_available: false,
      early_checkin_fee: 0,
    },
    childPricingPolicy: {
      age_groups: {
        infant: {
          min_age: 0,
          max_age: 4,
          price_percentage: 0,
          description: "Children 0-4 years stay free",
        },
        child: {
          min_age: 5,
          max_age: 13,
          price_percentage: 50,
          description: "Children 5-13 years pay 50% of adult rate",
        },
        adult: {
          min_age: 14,
          max_age: null,
          price_percentage: 100,
          description: "14+ years pay full adult rate",
        },
      },
      max_children_per_room: 3,
      infant_bed_required: false,
    },
    cancellationPolicy: {
      free_cancellation_days: 14,
      partial_refund_days: 7,
      partial_refund_percentage: 50,
      no_refund_days: 3,
      emergency_exceptions: true,
      policy_text: "",
    },
    pricingDefaults: {
      default_currency: "TZS",
      accepted_currencies: ["TZS", "USD", "EUR", "GBP"],
      tax_rate: 18,
      service_charge: 0,
      seasonal_pricing: true,
      discount_presets: [],
    },
    emailSettings: {
      sender_name: "",
      sender_email: "",
      reply_to: "",
      default_cc: [],
      default_bcc: [],
      booking_notifications: {
        new_booking: [],
        booking_confirmed: [],
        booking_cancelled: [],
        payment_received: [],
      },
    },
    emailTemplates: {
      booking_confirmation: { subject: "", html_body: "" },
      booking_update: { subject: "", html_body: "" },
      booking_cancellation: { subject: "", html_body: "" },
    },
    displaySettings: {
      theme: {
        primary_color: "#d97706",
        secondary_color: "#92400e",
        accent_color: "#f59e0b",
        background_color: "#fef3c7",
        text_color: "#1f2937",
      },
      logo_url: "",
      favicon_url: "",
      default_images: {
        accommodation_placeholder: "",
        activity_placeholder: "",
      },
      date_format: "YYYY-MM-DD",
      time_format: "HH:mm",
      currency_format: {
        symbol_position: "before",
        decimal_places: 0,
        thousands_separator: ",",
      },
    },
    businessInfo: {
      name: "Malombo Selous Forest Camp",
      tagline: "",
      description: "",
      established_year: 2010,
      license_number: "",
      social_media: {
        facebook: "",
        instagram: "",
        twitter: "",
      },
      certifications: [],
    },
    errors: [],
    isSubmitting: false,
    isDirty: false,
  });

  // Dialog states
  const [testEmailDialog, setTestEmailDialog] = useState({
    open: false,
    email: "",
    testing: false,
  });

  // Load all settings data
  const loadSettings = async () => {
    try {
      setLoading(true);

      const [
        contactsResult,
        checkInResult,
        childPricingResult,
        pricingResult,
        emailSettingsResult,
        emailTemplatesResult,
        displayResult,
        businessResult,
      ] = await Promise.all([
        getLodgeContacts(),
        getCheckInPolicy(),
        getChildPricingPolicy(),
        getPricingDefaults(),
        getEmailSettings(),
        getEmailTemplates(),
        getDisplaySettings(),
        getBusinessInfo(),
      ]);

      setFormData((prev) => ({
        ...prev,
        contacts: contactsResult.success ? contactsResult.data : prev.contacts,
        checkInPolicy: checkInResult.success
          ? checkInResult.data
          : prev.checkInPolicy,
        childPricingPolicy: childPricingResult.success
          ? childPricingResult.data
          : prev.childPricingPolicy,
        pricingDefaults: pricingResult.success
          ? pricingResult.data
          : prev.pricingDefaults,
        emailSettings: emailSettingsResult.success
          ? emailSettingsResult.data
          : prev.emailSettings,
        emailTemplates: emailTemplatesResult.success
          ? emailTemplatesResult.data
          : prev.emailTemplates,
        displaySettings: displayResult.success
          ? displayResult.data
          : prev.displaySettings,
        businessInfo: businessResult.success
          ? businessResult.data
          : prev.businessInfo,
        isDirty: false,
      }));
    } catch (error) {
      console.error("Error loading settings:", error);
      toast.error("Failed to load settings");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  // Form handlers
  const updateField = (
    section: keyof SettingsFormState,
    field: string,
    value: any
  ) => {
    setFormData((prev) => {
      const currentSection = prev[section];
      // Ensure we're working with object sections only
      if (
        section === "errors" ||
        section === "isSubmitting" ||
        section === "isDirty"
      ) {
        return prev; // Skip non-object sections
      }

      return {
        ...prev,
        [section]: {
          ...(currentSection as Record<string, any>),
          [field]: value,
        },
        isDirty: true,
        errors: prev.errors.filter(
          (error) => error.field !== `${section}.${field}`
        ),
      };
    });
  };

  const updateNestedField = (
    section: keyof SettingsFormState,
    path: string[],
    value: any
  ) => {
    setFormData((prev) => {
      const newSection = { ...(prev[section] as Record<string, any>) };
      let current = newSection;

      for (let i = 0; i < path.length - 1; i++) {
        current = current[path[i]] = { ...current[path[i]] };
      }
      current[path[path.length - 1]] = value;

      return {
        ...prev,
        [section]: newSection,
        isDirty: true,
        errors: prev.errors.filter(
          (error) => error.field !== `${section}.${path.join(".")}`
        ),
      };
    });
  };

  const addPhoneNumber = () => {
    setFormData((prev) => ({
      ...prev,
      contacts: {
        ...prev.contacts,
        phones: [...prev.contacts.phones, ""],
      },
      isDirty: true,
    }));
  };

  const removePhoneNumber = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      contacts: {
        ...prev.contacts,
        phones: prev.contacts.phones.filter((_, i) => i !== index),
      },
      isDirty: true,
    }));
  };

  const updatePhoneNumber = (index: number, value: string) => {
    setFormData((prev) => ({
      ...prev,
      contacts: {
        ...prev.contacts,
        phones: prev.contacts.phones.map((phone, i) =>
          i === index ? value : phone
        ),
      },
      isDirty: true,
    }));
  };

  const handleSave = async (section?: keyof SettingsFormState) => {
    try {
      setSaving(true);

      if (section) {
        // Save specific section
        let response;
        switch (section) {
          case "contacts":
            response = await updateLodgeContacts(formData.contacts);
            break;
          case "checkInPolicy":
            response = await updateCheckInPolicy(formData.checkInPolicy);
            break;
          case "childPricingPolicy":
            response = await updateChildPricingPolicy(
              formData.childPricingPolicy
            );
            break;
          case "pricingDefaults":
            response = await updatePricingDefaults(formData.pricingDefaults);
            break;
          case "emailSettings":
            response = await updateEmailSettings(formData.emailSettings);
            break;
          case "emailTemplates":
            response = await updateEmailTemplates(formData.emailTemplates);
            break;
          case "displaySettings":
            response = await updateDisplaySettings(formData.displaySettings);
            break;
          case "businessInfo":
            response = await updateBusinessInfo(formData.businessInfo);
            break;
          default:
            throw new Error("Invalid section");
        }

        if (response.success) {
          toast.success(response.message);
          setFormData((prev) => ({ ...prev, isDirty: false }));
        } else {
          toast.error(response.message);
        }
      } else {
        // Save all settings
        const allSettings = {
          lodge_contacts: formData.contacts,
          check_in_policy: formData.checkInPolicy,
          child_pricing_policy: formData.childPricingPolicy,
          pricing_defaults: formData.pricingDefaults,
          email_settings: formData.emailSettings,
          email_templates: formData.emailTemplates,
          display_settings: formData.displaySettings,
          business_info: formData.businessInfo,
        };

        const response = await bulkUpdateSettings(allSettings);
        if (response.success) {
          toast.success(
            `Updated ${response.updatedCount} settings successfully`
          );
          setFormData((prev) => ({ ...prev, isDirty: false }));
        } else {
          toast.error(response.message);
        }
      }
    } catch (error) {
      console.error("Error saving settings:", error);
      toast.error("Failed to save settings");
    } finally {
      setSaving(false);
    }
  };

  const handleTestEmail = async () => {
    if (!testEmailDialog.email) {
      toast.error("Please enter an email address");
      return;
    }

    try {
      setTestEmailDialog((prev) => ({ ...prev, testing: true }));
      const result = await testEmailConfiguration(testEmailDialog.email);

      if (result.success) {
        toast.success("Test email sent successfully!");
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error sending test email:", error);
      toast.error("Failed to send test email");
    } finally {
      setTestEmailDialog((prev) => ({ ...prev, testing: false }));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
      <div className="flex items-center justify-between">
        <TabsList className="grid w-full max-w-md grid-cols-4">
          <TabsTrigger value="contacts" className="flex items-center gap-2">
            <Phone className="w-4 h-4" />
            <span className="hidden sm:inline">Contacts</span>
          </TabsTrigger>
          <TabsTrigger value="policies" className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            <span className="hidden sm:inline">Policies</span>
          </TabsTrigger>
          <TabsTrigger value="pricing" className="flex items-center gap-2">
            <DollarSign className="w-4 h-4" />
            <span className="hidden sm:inline">Pricing</span>
          </TabsTrigger>
          <TabsTrigger value="email" className="flex items-center gap-2">
            <Mail className="w-4 h-4" />
            <span className="hidden sm:inline">Email</span>
          </TabsTrigger>
        </TabsList>

        <div className="flex items-center gap-2">
          {formData.isDirty && (
            <span className="text-sm text-amber-600">Unsaved changes</span>
          )}
          <Button
            onClick={() => handleSave()}
            disabled={saving || !formData.isDirty}
            className="bg-amber-600 hover:bg-amber-700"
          >
            <Save className="w-4 h-4 mr-2" />
            {saving ? "Saving..." : "Save All"}
          </Button>
        </div>
      </div>

      {/* Lodge Contacts Tab */}
      <TabsContent value="contacts" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Phone className="w-5 h-5 mr-2" />
              Lodge Contact Information
            </CardTitle>
            <CardDescription>
              Manage contact details for guest communications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Email Addresses */}
            <div>
              <h3 className="text-lg font-medium mb-4">Email Addresses</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="reservations-email">Reservations Email</Label>
                  <Input
                    id="reservations-email"
                    type="email"
                    value={formData.contacts.emails.reservations}
                    onChange={(e) =>
                      updateNestedField(
                        "contacts",
                        ["emails", "reservations"],
                        e.target.value
                      )
                    }
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="sales-email">Sales Email</Label>
                  <Input
                    id="sales-email"
                    type="email"
                    value={formData.contacts.emails.sales}
                    onChange={(e) =>
                      updateNestedField(
                        "contacts",
                        ["emails", "sales"],
                        e.target.value
                      )
                    }
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="info-email">General Info Email</Label>
                  <Input
                    id="info-email"
                    type="email"
                    value={formData.contacts.emails.info}
                    onChange={(e) =>
                      updateNestedField(
                        "contacts",
                        ["emails", "info"],
                        e.target.value
                      )
                    }
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="manager-email">Manager Email</Label>
                  <Input
                    id="manager-email"
                    type="email"
                    value={formData.contacts.emails.manager}
                    onChange={(e) =>
                      updateNestedField(
                        "contacts",
                        ["emails", "manager"],
                        e.target.value
                      )
                    }
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Phone Numbers */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">Phone Numbers</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addPhoneNumber}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Phone
                </Button>
              </div>
              <div className="space-y-3">
                {formData.contacts.phones.map((phone, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      value={phone}
                      onChange={(e) => updatePhoneNumber(index, e.target.value)}
                      placeholder="+255 123 456 789"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removePhoneNumber(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                {formData.contacts.phones.length === 0 && (
                  <p className="text-sm text-gray-500 text-center py-4">
                    No phone numbers added. Click "Add Phone" to add one.
                  </p>
                )}
              </div>
            </div>

            <Separator />

            {/* Address */}
            <div>
              <h3 className="text-lg font-medium mb-4">Physical Address</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor="street">Street Address</Label>
                  <Input
                    id="street"
                    value={formData.contacts.address.street}
                    onChange={(e) =>
                      updateNestedField(
                        "contacts",
                        ["address", "street"],
                        e.target.value
                      )
                    }
                    placeholder="Selous Game Reserve"
                  />
                </div>
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={formData.contacts.address.city}
                    onChange={(e) =>
                      updateNestedField(
                        "contacts",
                        ["address", "city"],
                        e.target.value
                      )
                    }
                    placeholder="Morogoro"
                  />
                </div>
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={formData.contacts.address.country}
                    onChange={(e) =>
                      updateNestedField(
                        "contacts",
                        ["address", "country"],
                        e.target.value
                      )
                    }
                    placeholder="Tanzania"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={() => handleSave("contacts")}
                disabled={saving}
                className="bg-amber-600 hover:bg-amber-700"
              >
                <Save className="w-4 h-4 mr-2" />
                {saving ? "Saving..." : "Save Contacts"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      {/* Policies Tab */}
      <TabsContent value="policies" className="space-y-6">
        {/* Check-in Policy */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              Check-in & Check-out Policy
            </CardTitle>
            <CardDescription>
              Set standard check-in and check-out times and fees
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="check-in-time">Check-in Time</Label>
                <Input
                  id="check-in-time"
                  type="time"
                  value={formData.checkInPolicy.check_in_time}
                  onChange={(e) =>
                    updateField(
                      "checkInPolicy",
                      "check_in_time",
                      e.target.value
                    )
                  }
                />
              </div>
              <div>
                <Label htmlFor="check-in-end">Check-in Ends</Label>
                <Input
                  id="check-in-end"
                  type="time"
                  value={formData.checkInPolicy.check_in_end}
                  onChange={(e) =>
                    updateField("checkInPolicy", "check_in_end", e.target.value)
                  }
                />
              </div>
              <div>
                <Label htmlFor="check-out-time">Check-out Time</Label>
                <Input
                  id="check-out-time"
                  type="time"
                  value={formData.checkInPolicy.check_out_time}
                  onChange={(e) =>
                    updateField(
                      "checkInPolicy",
                      "check_out_time",
                      e.target.value
                    )
                  }
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="late-checkout-fee">
                  Late Check-out Fee (USD)
                </Label>
                <Input
                  id="late-checkout-fee"
                  type="number"
                  min="0"
                  value={formData.checkInPolicy.late_checkout_fee}
                  onChange={(e) =>
                    updateField(
                      "checkInPolicy",
                      "late_checkout_fee",
                      parseFloat(e.target.value) || 0
                    )
                  }
                />
              </div>
              <div>
                <Label htmlFor="early-checkin-fee">
                  Early Check-in Fee (USD)
                </Label>
                <Input
                  id="early-checkin-fee"
                  type="number"
                  min="0"
                  value={formData.checkInPolicy.early_checkin_fee}
                  onChange={(e) =>
                    updateField(
                      "checkInPolicy",
                      "early_checkin_fee",
                      parseFloat(e.target.value) || 0
                    )
                  }
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="early-checkin-available"
                checked={formData.checkInPolicy.early_checkin_available}
                onCheckedChange={(checked) =>
                  updateField(
                    "checkInPolicy",
                    "early_checkin_available",
                    checked
                  )
                }
              />
              <Label htmlFor="early-checkin-available">
                Early check-in available
              </Label>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={() => handleSave("checkInPolicy")}
                disabled={saving}
                className="bg-amber-600 hover:bg-amber-700"
              >
                <Save className="w-4 h-4 mr-2" />
                {saving ? "Saving..." : "Save Policy"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      {/* Pricing Tab */}
      <TabsContent value="pricing" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="w-5 h-5 mr-2" />
              Pricing Defaults
            </CardTitle>
            <CardDescription>
              Configure default pricing settings and tax rates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="default-currency">Default Currency</Label>
                <Select
                  value={formData.pricingDefaults.default_currency}
                  onValueChange={(value) =>
                    updateField("pricingDefaults", "default_currency", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD - US Dollar</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                    <SelectItem value="GBP">GBP - British Pound</SelectItem>
                    <SelectItem value="TZS">
                      TZS - Tanzanian Shilling
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="tax-rate">Tax Rate (%)</Label>
                <Input
                  id="tax-rate"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={formData.pricingDefaults.tax_rate}
                  onChange={(e) =>
                    updateField(
                      "pricingDefaults",
                      "tax_rate",
                      parseFloat(e.target.value) || 0
                    )
                  }
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="service-charge">Service Charge (%)</Label>
                <Input
                  id="service-charge"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={formData.pricingDefaults.service_charge}
                  onChange={(e) =>
                    updateField(
                      "pricingDefaults",
                      "service_charge",
                      parseFloat(e.target.value) || 0
                    )
                  }
                />
              </div>
              <div className="flex items-center space-x-2 pt-6">
                <Switch
                  id="seasonal-pricing"
                  checked={formData.pricingDefaults.seasonal_pricing}
                  onCheckedChange={(checked) =>
                    updateField("pricingDefaults", "seasonal_pricing", checked)
                  }
                />
                <Label htmlFor="seasonal-pricing">
                  Enable seasonal pricing
                </Label>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={() => handleSave("pricingDefaults")}
                disabled={saving}
                className="bg-amber-600 hover:bg-amber-700"
              >
                <Save className="w-4 h-4 mr-2" />
                {saving ? "Saving..." : "Save Pricing"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      {/* Email Tab */}
      <TabsContent value="email" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="w-5 h-5 mr-2" />
              Email Configuration
            </CardTitle>
            <CardDescription>
              Configure email settings for notifications and communications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="sender-name">Sender Name</Label>
                <Input
                  id="sender-name"
                  value={formData.emailSettings.sender_name}
                  onChange={(e) =>
                    updateField("emailSettings", "sender_name", e.target.value)
                  }
                  placeholder="Malombo Selous Forest Camp"
                />
              </div>
              <div>
                <Label htmlFor="sender-email">Sender Email</Label>
                <Input
                  id="sender-email"
                  type="email"
                  value={formData.emailSettings.sender_email}
                  onChange={(e) =>
                    updateField("emailSettings", "sender_email", e.target.value)
                  }
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="reply-to">Reply-To Email</Label>
              <Input
                id="reply-to"
                type="email"
                value={formData.emailSettings.reply_to}
                onChange={(e) =>
                  updateField("emailSettings", "reply_to", e.target.value)
                }
                placeholder="<EMAIL>"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex-1">
                <Button
                  onClick={() => handleSave("emailSettings")}
                  disabled={saving}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {saving ? "Saving..." : "Save Email Settings"}
                </Button>
              </div>
              <Button
                variant="outline"
                onClick={() =>
                  setTestEmailDialog({ open: true, email: "", testing: false })
                }
              >
                <TestTube className="w-4 h-4 mr-2" />
                Test Email
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      {/* Test Email Dialog */}
      <Dialog
        open={testEmailDialog.open}
        onOpenChange={(open) =>
          setTestEmailDialog((prev) => ({ ...prev, open }))
        }
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Test Email Configuration</DialogTitle>
            <DialogDescription>
              Send a test email to verify your email configuration is working
              correctly.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="test-email">Test Email Address</Label>
              <Input
                id="test-email"
                type="email"
                value={testEmailDialog.email}
                onChange={(e) =>
                  setTestEmailDialog((prev) => ({
                    ...prev,
                    email: e.target.value,
                  }))
                }
                placeholder="<EMAIL>"
              />
            </div>

            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                This will send a test email using your current email
                configuration. Make sure you have saved your email settings
                before testing.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() =>
                setTestEmailDialog({ open: false, email: "", testing: false })
              }
            >
              Cancel
            </Button>
            <Button
              onClick={handleTestEmail}
              disabled={!testEmailDialog.email || testEmailDialog.testing}
            >
              {testEmailDialog.testing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sending...
                </>
              ) : (
                <>
                  <TestTube className="w-4 h-4 mr-2" />
                  Send Test Email
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Tabs>
  );
}
