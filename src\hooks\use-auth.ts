import { useState, useEffect, createContext, useContext } from "react";
import { supabase } from "@/lib/supabase";
import type { User } from "@supabase/supabase-js";

// Simple auth state interface - focusing on core functionality
interface AuthState {
  user: User | null;
  loading: boolean;
  initialized: boolean;
}

// Auth context with essential methods
interface AuthContextType extends AuthState {
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}

// Create auth context
const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  initialized: false,
  signIn: async () => {},
  signOut: async () => {},
});

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// Main auth state hook
export const useAuthState = (): AuthContextType => {
  const [state, setState] = useState<AuthState>({
    user: null,
    loading: true,
    initialized: false,
  });

  // Sign in function
  const signIn = async (email: string, password: string): Promise<void> => {
    setState((prev) => ({ ...prev, loading: true }));

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // State will be updated by onAuthStateChange listener
    } catch (error) {
      setState((prev) => ({ ...prev, loading: false }));
      throw error;
    }
  };

  // Sign out function
  const signOut = async (): Promise<void> => {
    setState((prev) => ({ ...prev, loading: true }));

    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      // State will be updated by onAuthStateChange listener
    } catch (error) {
      console.error("Sign out error:", error);
      // Force clear state even if signOut fails
      setState({
        user: null,
        loading: false,
        initialized: true,
      });
    }
  };

  // Initialize auth and set up listeners
  useEffect(() => {
    let mounted = true;

    // Initialize session check
    const initializeAuth = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (mounted) {
          setState({
            user: session?.user || null,
            loading: false,
            initialized: true,
          });
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
        if (mounted) {
          setState({
            user: null,
            loading: false,
            initialized: true,
          });
        }
      }
    };

    // Set up auth state change listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return;

      // console.log("Auth state change:", event);

      switch (event) {
        case "INITIAL_SESSION":
          // Handle initial session
          setState({
            user: session?.user || null,
            loading: false,
            initialized: true,
          });
          break;
        case "SIGNED_IN":
          setState({
            user: session?.user || null,
            loading: false,
            initialized: true,
          });
          break;
        case "SIGNED_OUT":
          setState({
            user: null,
            loading: false,
            initialized: true,
          });
          break;
        case "TOKEN_REFRESHED":
          // Session refreshed, update user if needed
          setState((prev) => ({
            ...prev,
            user: session?.user || null,
          }));
          break;
      }
    });

    // Initialize auth
    initializeAuth();

    // Cleanup
    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  return {
    ...state,
    signIn,
    signOut,
  };
};

export { AuthContext };
